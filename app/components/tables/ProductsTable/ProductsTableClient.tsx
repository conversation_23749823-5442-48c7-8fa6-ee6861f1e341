"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';

// Import StandardizedTable and column definitions
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import {
  createProductsSimpleColumns,
  createProductsComplexColumns,
  type ProductColumnData,
  type ProductsTableActions
} from '@/app/components/data-display/data-table';
import { getApiUrl } from '@/app/utils/apiUtils';

import { Product, ProductsTableProps } from './types';

/**
 * Client component implementation of ProductsTable using DataTable
 * Handles all interactive logic and rendering with modern DataTable component
 */
export default function ProductsTableClient({ products, simple = false, onDeletePart }: ProductsTableProps) {
  const router = useRouter();

  /**
   * Handle refresh after actions
   */
  const handleRefresh = () => {
    router.refresh();
  };

  /**
   * Handle product deletion with confirmation
   */
  const handleDelete = async (product: ProductColumnData) => {
    confirmAlert({
      title: "Confirm Deletion",
      message: `Are you sure you want to delete product "${product.name}" (${product.id || product.productCode})?`,
      buttons: [
        {
          label: 'Yes',
          onClick: async () => {
            try {
              const response = await fetch(getApiUrl(`/api/products/${product._id}`), {
                method: 'DELETE',
              });

              if (response.ok) {
                toast.success(`Product ${product.id || product.productCode} deleted successfully`);

                // Call the onDeletePart callback if provided
                if (onDeletePart) {
                  const idToDelete = product._id || product.id || '';
                  console.log('[ProductsTableClient] Calling onDeletePart with ID:', idToDelete);
                  await onDeletePart(idToDelete).catch(error => {
                    console.error('Error in onDeletePart callback:', error);
                  });
                }

                // Always refresh the UI
                handleRefresh();
              } else {
                const data = await response.json() as { error?: string; message?: string };
                throw new Error(data.error || "Failed to delete product");
              }
            } catch (error) {
              toast.error(error instanceof Error ? error.message : "An error occurred while deleting the product");
              console.error("Delete error:", error);
            }
          }
        },
        {
          label: 'No',
          onClick: () => {}
        }
      ]
    });
  };

  // Convert Product[] to ProductColumnData[] for compatibility
  const tableData: ProductColumnData[] = products.map(product => ({
    ...product,
    // Ensure all required fields are present
    _id: product._id,
    id: product.id,
    productCode: product.productCode,
    name: product.name,
    description: product.description,
    categoryId: product.categoryId,
    status: product.status,
    sellingPrice: product.sellingPrice,
    assemblyId: product.assemblyId ?? null,
    partId: product.partId ?? null,
    currentStock: product.currentStock ?? 0,
    reorderLevel: product.reorderLevel ?? 0,
    supplierManufacturer: product.supplierManufacturer ?? '',
    inventory: product.inventory ?? { currentStock: 0 },
  }));

  // Define actions for the table
  const actions: ProductsTableActions = {
    onView: (product) => {
      // View action will be handled by the ViewProductButton in column definitions
      // This is kept for compatibility but won't be used
      console.log('View product:', product._id);
    },
    onEdit: (product) => {
      router.push(`/products/${product._id}/edit`);
    },
    onDelete: handleDelete,
    onRefresh: handleRefresh,
  };

  // Get appropriate column definitions based on mode
  const columns = simple
    ? createProductsSimpleColumns(actions)
    : createProductsComplexColumns(actions);

  if (products.length === 0) {
    return null;
  }

  return (
    <StandardizedTable
      data={tableData}
      columns={columns}
      searchPlaceholder="Search products..."
      enableSearch={true}
      enableViewToggle={false}
      tableProps={{
        enableSorting: true,
        enableFiltering: true,
        enableGlobalSearch: false, // Using StandardizedTable's search instead
        enablePagination: true,
        enableColumnVisibility: false,
        mobileDisplayMode: "cards",
        density: simple ? "compact" : "normal",
        initialPagination: { pageIndex: 0, pageSize: 10 },
        pageSizeOptions: [10, 20, 50, 100],
        caption: `${simple ? 'Simple' : 'Detailed'} products table with ${products.length} items`,
        onRowClick: (product: unknown) => {
          // Navigate to product detail page on row click (except for simple mode)
          if (!simple) {
            const typedProduct = product as Product;
            router.push(`/products/${typedProduct._id}`);
          }
        },
      }}
    />
  );
}
