const { chromium } = require('playwright');

async function testInventoryPagination() {
  console.log('🔍 Starting Inventory Pagination Test...');
  
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to inventory page
    console.log('📍 Navigating to inventory page...');
    await page.goto('http://localhost:3001/inventory');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Check if there's any data in the table
    console.log('🔍 Checking for table data...');
    const tableRows = await page.locator('table tbody tr').count();
    console.log(`📊 Found ${tableRows} table rows`);

    if (tableRows === 0) {
      console.log('⚠️  No data found in inventory table');
      
      // Check if there's an empty state message
      const emptyState = await page.locator('text=No inventory data').isVisible();
      if (emptyState) {
        console.log('✅ Empty state is properly displayed');
      }
      
      // Check if pagination controls are still present
      const paginationControls = await page.locator('[data-testid="pagination"], .pagination, button:has-text("Next"), button:has-text("Previous")').count();
      console.log(`📄 Found ${paginationControls} pagination controls`);
      
    } else {
      console.log('✅ Data found in inventory table');
      
      // Test pagination controls
      console.log('🔍 Testing pagination controls...');
      
      // Look for pagination info
      const paginationInfo = await page.locator('text=/Showing \\d+ to \\d+ of \\d+ results/').textContent().catch(() => null);
      if (paginationInfo) {
        console.log(`📊 Pagination info: ${paginationInfo}`);
      }
      
      // Look for Next button
      const nextButton = page.locator('button:has-text("Next"), button[aria-label*="next"], button[aria-label*="Next"]').first();
      const nextButtonExists = await nextButton.isVisible();
      console.log(`➡️  Next button visible: ${nextButtonExists}`);
      
      if (nextButtonExists) {
        const isNextEnabled = await nextButton.isEnabled();
        console.log(`➡️  Next button enabled: ${isNextEnabled}`);
        
        if (isNextEnabled) {
          console.log('🔄 Clicking Next button...');
          
          // Get current data before clicking
          const currentData = await page.locator('table tbody tr').first().textContent();
          console.log(`📋 Current first row: ${currentData?.substring(0, 50)}...`);
          
          // Click next button
          await nextButton.click();
          await page.waitForTimeout(1000);
          
          // Get new data after clicking
          const newData = await page.locator('table tbody tr').first().textContent();
          console.log(`📋 New first row: ${newData?.substring(0, 50)}...`);
          
          if (currentData !== newData) {
            console.log('✅ SUCCESS: Pagination changed the displayed data!');
          } else {
            console.log('❌ ISSUE: Pagination did not change the displayed data');
          }
        }
      }
      
      // Look for page size selector
      const pageSizeSelector = page.locator('select, [role="combobox"]').first();
      const pageSizeSelectorExists = await pageSizeSelector.isVisible();
      console.log(`📏 Page size selector visible: ${pageSizeSelectorExists}`);
    }

    // Check for any console errors
    console.log('🔍 Checking for console errors...');
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Console Error: ${msg.text()}`);
      } else if (msg.type() === 'log' && msg.text().includes('INVENTORY DEBUG')) {
        console.log(`🔧 Debug: ${msg.text()}`);
      }
    });

    // Take a screenshot for verification
    await page.screenshot({ path: 'inventory-pagination-test.png', fullPage: true });
    console.log('📸 Screenshot saved as inventory-pagination-test.png');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testInventoryPagination().then(() => {
  console.log('🏁 Test completed');
}).catch(error => {
  console.error('💥 Test crashed:', error);
});
